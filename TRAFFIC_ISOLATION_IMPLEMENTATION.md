# WireGuard Traffic Isolation Implementation

## Overview

This implementation adds fwmark-based traffic isolation to the WireGuard management service, allowing multiple WireGuard interfaces to have overlapping AllowedIPs configurations without routing conflicts.

## Key Features

1. **Automatic FwMark Allocation**: Each WireGuard interface gets a unique firewall mark (starting from 0x1000)
2. **Custom Routing Tables**: Each interface uses a separate routing table (starting from table 100)
3. **Policy-Based Routing**: Traffic is routed based on fwmark using ip rules
4. **Packet Marking**: Outbound packets are marked before routing decisions (supports both nftables and iptables)
5. **API Compatibility**: All existing APIs continue to work without breaking changes

## Implementation Details

### New Components

1. **FwMark Management** (`service/wireguardmgr/fwmark.go`)
   - Allocates unique fwmark values (0x1000-0xFFFF)
   - Allocates unique routing table numbers (100-999)
   - Persists allocations in MongoDB (`wg_fwmarks` collection)

2. **Traffic Isolation Manager** (`service/wireguardmgr/traffic_isolation.go`)
   - Sets up custom routing tables
   - Manages ip rules for fwmark-based routing
   - Handles packet marking (nftables/iptables)
   - Manages peer routes in custom tables

### Modified Components

1. **Device Model** (`service/wireguardmgr/model.go`)
   - Added `FwMark` and `RoutingTable` fields to Device struct

2. **WireGuard Configuration** (`service/wireguardmgr/wghelper.go`)
   - Added `FwMark = 0x<value>` to interface configuration
   - Added `Table = off` to disable automatic route management

3. **Device Management** (`service/wireguardmgr/manager.go`)
   - Integrated fwmark allocation in CreateDevice
   - Added traffic isolation setup/cleanup
   - Updated peer route management

4. **Initialization** (`service/wireguardmgr/init.go`)
   - Added database indexes for fwmark collection
   - Added traffic isolation restoration on startup

## How It Works

### Interface Creation
1. Allocate unique fwmark and routing table number
2. Generate WireGuard config with `FwMark` and `Table = off`
3. Start WireGuard interface
4. Set up traffic isolation:
   - Create ip rule: `fwmark 0x<mark> table <table>`
   - Set up packet marking rules (nftables/iptables)
   - Add device and peer routes to custom table

### Traffic Flow
1. Outbound packets from WireGuard interface get marked with fwmark
2. Kernel routing uses ip rules to route marked packets via custom table
3. Custom table contains only routes for this specific interface
4. No conflicts with other interfaces having overlapping AllowedIPs

### Peer Route Management
- Peer's own IP (/32) is automatically added to custom table
- Additional AllowedIPs from `peer_routes` collection are also added
- Routes are updated when peer AllowedIPs change
- Routes are removed when peers are deleted

## Configuration Example

### Before (Standard WireGuard)
```ini
[Interface]
PrivateKey = <key>
Address = ********/24
ListenPort = 50000
```

### After (With Traffic Isolation)
```ini
[Interface]
PrivateKey = <key>
Address = ********/24
ListenPort = 50000
FwMark = 0x1000
Table = off
```

## Database Schema Changes

### New Collection: `wg_fwmarks`
```json
{
  "_id": ObjectId,
  "enterprise_id": "string",
  "device_name": "string", 
  "fwmark": 4096,
  "routing_table": 100,
  "created_at": ISODate
}
```

### Updated Collection: `wg_devices`
```json
{
  // ... existing fields ...
  "fwmark": 4096,
  "routing_table": 100
}
```

## Testing the Implementation

### 1. Create Multiple Interfaces with Overlapping AllowedIPs

```bash
# Create first interface
curl -X POST http://localhost:8080/api/wg/device \
  -H "Content-Type: application/json" \
  -d '{"name": "wg1", "enterprise_id": "enterprise1"}'

# Create second interface  
curl -X POST http://localhost:8080/api/wg/device \
  -H "Content-Type: application/json" \
  -d '{"name": "wg2", "enterprise_id": "enterprise2"}'
```

### 2. Add Peers with Same AllowedIPs

```bash
# Add peer to wg1 with IP ************
curl -X POST http://localhost:8080/api/wg/peer \
  -H "Content-Type: application/json" \
  -d '{"name": "peer1", "enterprise_id": "enterprise1", "device_name": "wg1"}'

# Update peer1 AllowedIPs to include ************/32
curl -X POST http://localhost:8080/api/wg/peer/allowed-ips \
  -H "Content-Type: application/json" \
  -d '{"device_name": "wg1", "enterprise_id": "enterprise1", "peer_id": "<peer1_id>", "allowed_ips": ["************/32"]}'

# Add peer to wg2 with same IP ************  
curl -X POST http://localhost:8080/api/wg/peer \
  -H "Content-Type: application/json" \
  -d '{"name": "peer2", "enterprise_id": "enterprise2", "device_name": "wg2"}'

# Update peer2 AllowedIPs to include ************/32
curl -X POST http://localhost:8080/api/wg/peer/allowed-ips \
  -H "Content-Type: application/json" \
  -d '{"device_name": "wg2", "enterprise_id": "enterprise2", "peer_id": "<peer2_id>", "allowed_ips": ["************/32"]}'
```

### 3. Verify Traffic Isolation

```bash
# Check ip rules
ip rule show

# Check custom routing tables
ip route show table 100
ip route show table 101

# Check packet marking rules
# For nftables:
nft list tables
nft list table inet wg_wg1
nft list table inet wg_wg2

# For iptables:
iptables -t mangle -L OUTPUT -v
```

### 4. Verify No Routing Conflicts

Both interfaces should be able to handle traffic to ************/32 independently without conflicts.

## Backward Compatibility

- All existing API endpoints continue to work unchanged
- Existing WireGuard interfaces without fwmark continue to work normally
- New interfaces automatically get traffic isolation
- No breaking changes to client applications

## System Requirements

- Linux kernel with support for:
  - Policy routing (`ip rule`)
  - Custom routing tables
  - Firewall marks
  - nftables or iptables
- WireGuard kernel module or userspace implementation

## Troubleshooting

### Check Traffic Isolation Status
```bash
# List fwmark allocations
mongo cloud_bak --eval "db.wg_fwmarks.find().pretty()"

# Check if rules are applied
ip rule show | grep fwmark

# Check custom tables
for i in {100..110}; do
  echo "Table $i:"
  ip route show table $i
done
```

### Common Issues

1. **Missing ip rules**: Check if `ip rule add fwmark 0x<mark> table <table>` commands succeeded
2. **Missing packet marking**: Verify nftables/iptables rules are in place
3. **Route conflicts**: Ensure `Table = off` is in WireGuard config
4. **Permission issues**: Ensure service has CAP_NET_ADMIN capability

## Performance Impact

- Minimal overhead from packet marking and custom routing
- No impact on existing interfaces without traffic isolation
- Routing table lookups are efficient in Linux kernel
- Memory usage increases slightly for additional routing tables
